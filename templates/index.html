<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .chat-container {
            height: calc(100vh - 4rem);
        }
        .preview-container {
            height: calc(100vh - 4rem);
        }
        .message-content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .typing-indicator {
            animation: pulse 1.5s ease-in-out infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .preview-iframe {
            border: none;
            width: 100%;
            height: 100%;
            background: white;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- 左侧对话区 -->
        <div class="w-1/2 bg-white border-r border-gray-300 flex flex-col">
            <!-- 头部 -->
            <div class="bg-blue-600 text-white p-4">
                <h1 class="text-xl font-bold">AI Code Generator</h1>
                <p class="text-sm opacity-90">基于 Qwen3-Coder 的HTML代码生成器</p>
            </div>
            
            <!-- 聊天消息区 -->
            <div id="chatMessages" class="flex-1 overflow-y-auto p-4 space-y-4 chat-container">
                <div class="bg-blue-50 p-3 rounded-lg">
                    <div class="text-sm text-blue-600 font-medium mb-1">系统</div>
                    <div class="text-gray-700">欢迎使用AI代码生成器！请描述您想要创建的网页，我会为您生成完整的HTML代码。</div>
                </div>
            </div>
            
            <!-- 输入区 -->
            <div class="border-t border-gray-200 p-4">
                <div class="flex space-x-2">
                    <input 
                        type="text" 
                        id="messageInput" 
                        placeholder="描述您想要的网页..." 
                        class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        onkeypress="handleKeyPress(event)"
                    >
                    <button 
                        id="sendButton" 
                        onclick="sendMessage()" 
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        发送
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 右侧预览区 -->
        <div class="w-1/2 bg-gray-50 flex flex-col">
            <!-- 头部 -->
            <div class="bg-gray-800 text-white p-4 flex justify-between items-center">
                <h2 class="text-lg font-semibold">代码预览</h2>
                <button 
                    id="deployButton" 
                    onclick="deployCode()" 
                    class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-500 disabled:cursor-not-allowed"
                    disabled
                >
                    部署到 EdgeOne
                </button>
            </div>
            
            <!-- 预览区域 -->
            <div class="flex-1 p-4 preview-container">
                <div id="previewArea" class="w-full h-full bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="flex items-center justify-center h-full text-gray-500">
                        <div class="text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                            </svg>
                            <p class="mt-2">等待生成代码...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws;
        let currentCode = '';
        let messages = [];
        let isGenerating = false;

        // 初始化WebSocket连接
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
            
            ws.onopen = function() {
                console.log('WebSocket连接已建立');
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.error) {
                    addMessage('系统', `错误: ${data.error}`, 'error');
                    isGenerating = false;
                    updateSendButton();
                } else if (data.done) {
                    isGenerating = false;
                    updateSendButton();
                    updatePreview();
                    document.getElementById('deployButton').disabled = false;
                } else if (data.content) {
                    appendToLastMessage(data.content);
                }
            };
            
            ws.onclose = function() {
                console.log('WebSocket连接已关闭');
                setTimeout(initWebSocket, 3000); // 3秒后重连
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isGenerating) return;
            
            messages.push({role: 'user', content: message});
            addMessage('用户', message, 'user');
            
            input.value = '';
            isGenerating = true;
            updateSendButton();
            
            // 添加AI消息占位符
            addMessage('AI', '', 'assistant', true);
            
            // 发送到WebSocket
            ws.send(JSON.stringify({messages: messages}));
        }

        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 添加消息到聊天区
        function addMessage(sender, content, type, isTyping = false) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            
            let bgColor = 'bg-gray-50';
            let textColor = 'text-gray-600';
            
            if (type === 'user') {
                bgColor = 'bg-blue-100';
                textColor = 'text-blue-600';
            } else if (type === 'assistant') {
                bgColor = 'bg-green-50';
                textColor = 'text-green-600';
            } else if (type === 'error') {
                bgColor = 'bg-red-50';
                textColor = 'text-red-600';
            }
            
            messageDiv.className = `${bgColor} p-3 rounded-lg`;
            messageDiv.innerHTML = `
                <div class="text-sm ${textColor} font-medium mb-1">${sender}</div>
                <div class="message-content text-gray-700 ${isTyping ? 'typing-indicator' : ''}">${content}</div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 追加内容到最后一条消息
        function appendToLastMessage(content) {
            const chatMessages = document.getElementById('chatMessages');
            const lastMessage = chatMessages.lastElementChild;
            if (lastMessage) {
                const contentDiv = lastMessage.querySelector('.message-content');
                if (contentDiv) {
                    contentDiv.textContent += content;
                    contentDiv.classList.remove('typing-indicator');
                    currentCode += content;
                }
            }
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 更新发送按钮状态
        function updateSendButton() {
            const button = document.getElementById('sendButton');
            if (isGenerating) {
                button.textContent = '生成中...';
                button.disabled = true;
            } else {
                button.textContent = '发送';
                button.disabled = false;
            }
        }

        // 更新预览
        function updatePreview() {
            const previewArea = document.getElementById('previewArea');
            if (currentCode.trim()) {
                previewArea.innerHTML = `<iframe class="preview-iframe" srcdoc="${currentCode.replace(/"/g, '&quot;')}"></iframe>`;
            }
        }

        // 部署代码
        async function deployCode() {
            if (!currentCode.trim()) {
                alert('没有可部署的代码');
                return;
            }
            
            const deployButton = document.getElementById('deployButton');
            deployButton.textContent = '部署中...';
            deployButton.disabled = true;
            
            try {
                const response = await fetch('/api/deploy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({code: currentCode})
                });
                
                const result = await response.json();
                
                if (result.url) {
                    addMessage('系统', `部署成功！访问地址: ${result.url}`, 'success');
                    window.open(result.url, '_blank');
                } else {
                    addMessage('系统', `部署响应: ${JSON.stringify(result)}`, 'info');
                }
            } catch (error) {
                addMessage('系统', `部署失败: ${error.message}`, 'error');
            } finally {
                deployButton.textContent = '部署到 EdgeOne';
                deployButton.disabled = false;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
        });
    </script>
</body>
</html>
