package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"io"
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

type ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type ChatRequest struct {
	Messages []ChatMessage `json:"messages"`
}

type DeployRequest struct {
	Code string `json:"code"`
}

type DeployResponse struct {
	URL string `json:"url"`
}

const (
	ModelScopeBaseURL = "https://api-inference.modelscope.cn"
	ModelScopeAPIKey  = "sk-test"
	ModelName         = "Qwen/Qwen3-Coder-480B-A35B-Instruct"
	SystemPrompt      = "ONLY USE HTML, CSS AND JAVASCRIPT. If you want to use ICON make sure to import the library first. Try to create the best UI possible by using only HTML, CSS and JAVASCRIPT. MAKE IT RESPONSIVE USING TAILWINDCSS. Use as much as you can TailwindCSS for the CSS, if you can't do something with TailwindCSS, then use custom CSS (make sure to import <script src=\"https://cdn.tailwindcss.com\"></script> in the head). Also, try to ellaborate as much as you can, to create something unique. ALWAYS GIVE THE RESPONSE INTO A SINGLE HTML FILE"
)

func main() {
	r := gin.Default()

	// 静态文件服务
	r.Static("/static", "./static")
	r.LoadHTMLGlob("templates/*")

	// 主页
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", nil)
	})

	// WebSocket连接
	r.GET("/ws", handleWebSocket)

	// 部署API
	r.POST("/api/deploy", handleDeploy)

	log.Println("Server starting on :8080")
	r.Run(":8080")
}

func handleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}
	defer conn.Close()

	for {
		var req ChatRequest
		err := conn.ReadJSON(&req)
		if err != nil {
			log.Printf("WebSocket read error: %v", err)
			break
		}

		// 添加系统提示
		messages := []ChatMessage{
			{Role: "system", Content: SystemPrompt},
		}
		messages = append(messages, req.Messages...)

		// 调用ModelScope API
		err = streamChatResponse(conn, messages)
		if err != nil {
			log.Printf("Stream chat error: %v", err)
			conn.WriteJSON(map[string]string{"error": err.Error()})
		}
	}
}

func streamChatResponse(conn *websocket.Conn, messages []ChatMessage) error {
	reqBody := map[string]interface{}{
		"model":    ModelName,
		"messages": messages,
		"stream":   true,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", ModelScopeBaseURL+"/v1/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+ModelScopeAPIKey)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		line := scanner.Text()
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")
			if data == "[DONE]" {
				break
			}

			var streamResp map[string]interface{}
			if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
				continue
			}

			if choices, ok := streamResp["choices"].([]interface{}); ok && len(choices) > 0 {
				if choice, ok := choices[0].(map[string]interface{}); ok {
					if delta, ok := choice["delta"].(map[string]interface{}); ok {
						if content, ok := delta["content"].(string); ok {
							err := conn.WriteJSON(map[string]string{"content": content})
							if err != nil {
								return err
							}
						}
					}
				}
			}
		}
	}

	// 发送结束信号
	return conn.WriteJSON(map[string]string{"done": "true"})
}

func handleDeploy(c *gin.Context) {
	var req DeployRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 调用EdgeOne部署API
	deployData := map[string]string{
		"value": req.Code,
	}

	jsonData, err := json.Marshal(deployData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	deployReq, err := http.NewRequest("POST", "https://mcp.edgeone.site/kv/set", bytes.NewBuffer(jsonData))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	deployReq.Header.Set("Content-Type", "application/json")
	deployReq.Header.Set("X-Installation-ID", "modelscopefortest")

	client := &http.Client{}
	resp, err := client.Do(deployReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var deployResp map[string]interface{}
	if err := json.Unmarshal(body, &deployResp); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 返回部署URL
	if url, ok := deployResp["url"].(string); ok {
		c.JSON(http.StatusOK, DeployResponse{URL: url})
	} else {
		c.JSON(http.StatusOK, gin.H{"response": string(body)})
	}
}
