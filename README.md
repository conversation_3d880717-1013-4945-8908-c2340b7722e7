# AI Chat App - HTML代码生成器

基于Go和Gin框架开发的AI对话Web应用，集成ModelScope API，支持流式输出、实时预览和一键部署功能。

## 功能特性

- 🤖 **AI对话**: 集成Qwen3-Coder模型，专门用于HTML代码生成
- 💬 **流式输出**: 实时显示AI生成的内容，类似Claude.ai的体验
- 👀 **实时预览**: 右侧实时预览生成的HTML代码效果
- 🚀 **一键部署**: 支持将生成的代码部署到EdgeOne平台
- 📱 **响应式设计**: 使用TailwindCSS构建的现代化界面

## 技术栈

- **后端**: Go + Gin框架
- **前端**: HTML + JavaScript + TailwindCSS
- **通信**: WebSocket实现实时通信
- **AI模型**: Qwen/Qwen3-Coder-480B-A35B-Instruct
- **部署**: EdgeOne平台

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 启动服务器

```bash
go run main.go
```

### 3. 访问应用

打开浏览器访问: http://localhost:8080

## 使用说明

1. **发起对话**: 在左侧输入框中描述你想要的网页功能
2. **查看生成**: AI会流式生成HTML代码，实时显示在对话区
3. **预览效果**: 右侧会自动显示生成代码的预览效果
4. **部署上线**: 点击"部署到EdgeOne"按钮一键部署

## 系统提示词

应用使用以下系统提示词确保生成高质量的HTML代码：

```
ONLY USE HTML, CSS AND JAVASCRIPT. If you want to use ICON make sure to import the library first. Try to create the best UI possible by using only HTML, CSS and JAVASCRIPT. MAKE IT RESPONSIVE USING TAILWINDCSS. Use as much as you can TailwindCSS for the CSS, if you can't do something with TailwindCSS, then use custom CSS (make sure to import <script src="https://cdn.tailwindcss.com"></script> in the head). Also, try to ellaborate as much as you can, to create something unique. ALWAYS GIVE THE RESPONSE INTO A SINGLE HTML FILE
```

## API配置

- **ModelScope API**: https://api-inference.modelscope.cn
- **模型**: Qwen/Qwen3-Coder-480B-A35B-Instruct
- **API Key**: sk-test (硬编码用于测试)
- **部署平台**: EdgeOne (https://mcp.edgeone.site/kv/set)

## 项目结构

```
.
├── main.go              # 主服务器文件
├── templates/
│   └── index.html       # 前端页面模板
├── static/              # 静态资源目录
├── go.mod              # Go模块文件
├── go.sum              # 依赖校验文件
└── README.md           # 项目说明
```

## 主要功能实现

### WebSocket通信
- 实时双向通信
- 流式数据传输
- 自动重连机制

### AI集成
- ModelScope API调用
- 流式响应处理
- 错误处理和重试

### 代码预览
- iframe实时渲染
- 安全的HTML注入
- 响应式预览窗口

### 部署功能
- EdgeOne API集成
- 一键部署流程
- 部署状态反馈

## 开发说明

应用在8080端口运行，支持以下路由：

- `GET /`: 主页面
- `GET /ws`: WebSocket连接
- `POST /api/deploy`: 部署API
- `GET /static/*`: 静态文件服务

## 注意事项

1. 确保网络连接正常，需要访问ModelScope API
2. 部署功能需要EdgeOne平台支持
3. 建议在生产环境中使用真实的API密钥
4. WebSocket连接断开会自动重连

## 许可证

MIT License
